#!/usr/bin/env python3
"""
Permanent Graphiti MCP Server with SSE Transport

This script runs a permanent Graphiti MCP server using Server-Sent Events (SSE) transport.
The server will run continuously and accept connections from SSE clients.

Usage:
    python run_permanent_sse_server.py [--host HOST] [--port PORT]

Default: http://localhost:8000/sse
"""

import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the mcp_server directory to the path so we can import the server
mcp_server_path = Path(__file__).parent / "mcp_server"
sys.path.insert(0, str(mcp_server_path))

from graphiti_mcp_server import run_mcp_server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
)
logger = logging.getLogger(__name__)


def setup_environment():
    """Set up environment variables for the Graphiti MCP server."""
    # Default environment variables
    env_defaults = {
        "NEO4J_URI": "bolt://localhost:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "demodemo",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "MODEL_NAME": "gpt-4.1-mini",
        "TEMPERATURE": "0.7",
        "MAX_TOKENS": "4000",
        "USE_CUSTOM_ENTITIES": "true",
        "MAXIMUM_ABSTRACTIONS": "30",
    }
    
    # Set environment variables if not already set
    for key, value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = value
            logger.info(f"Set {key} to default value")
        else:
            logger.info(f"Using existing {key}")


async def run_permanent_server(host: str = "localhost", port: int = 8000):
    """Run the permanent SSE server."""
    logger.info("🚀 Starting Permanent Graphiti MCP Server with SSE Transport")
    logger.info(f"📡 Server will be available at: http://{host}:{port}/sse")
    logger.info("🔄 Server will run continuously until stopped (Ctrl+C)")
    
    # Set up environment
    setup_environment()
    
    # Set transport and server settings via environment
    os.environ["TRANSPORT"] = "sse"
    os.environ["HOST"] = host
    os.environ["PORT"] = str(port)
    
    try:
        # Run the MCP server
        await run_mcp_server()
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        raise


def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="Run permanent Graphiti MCP server with SSE transport"
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="Host to bind the server to (default: localhost)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    # Run the server
    try:
        asyncio.run(run_permanent_server(args.host, args.port))
    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
