# Memory System Architecture

## Overview

This document describes the architecture of agent-zero's memory system before and after the Graphiti temporal knowledge graph integration.

## Before: FAISS-Based Memory System

### Architecture Diagram

```mermaid
graph TB
    A[Agent Core Logic] --> B[Memory Tools]
    A --> C[Memory Extensions]
    
    B --> D[memory_save.py]
    B --> E[memory_load.py] 
    B --> F[memory_delete.py]
    B --> G[memory_forget.py]
    
    C --> H[RecallMemories]
    C --> I[RecallSolutions]
    C --> J[MemorizeMemories]
    C --> K[MemorizeSolutions]
    
    D --> L[Memory Helper]
    E --> L
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    
    L --> M[FAISS Vector Database]
    L --> N[Embeddings Cache]
    
    M --> O[Local File Storage]
    N --> P[Local File Storage]
    
    style A fill:#e1f5fe
    style L fill:#fff3e0
    style M fill:#f3e5f5
    style O fill:#e8f5e8
```

### Components

**Memory Helper (`python/helpers/memory.py`)**
- Singleton factory pattern with static index
- FAISS vector database management
- Document storage with metadata
- Similarity search with threshold filtering
- Memory areas: MAIN, FRAGMENTS, SOLUTIONS, INSTRUMENTS

**Memory Tools**
- Direct interface for agent memory operations
- Simple CRUD operations on memory documents
- Metadata-based filtering and search

**Memory Extensions**
- Automated memory management
- Background recall and memorization
- Integration with agent's thought loop

### Data Flow

1. **Memory Save**: Text → Embeddings → FAISS Index → Local Storage
2. **Memory Load**: Query → Embeddings → FAISS Search → Filtered Results
3. **Memory Delete**: IDs → FAISS Delete → Index Update
4. **Auto Recall**: History → Query Generation → Memory Search → Prompt Injection

## After: Graphiti Temporal Knowledge Graph

### Architecture Diagram

```mermaid
graph TB
    A[Agent Core Logic] --> B[Memory Abstraction Layer]
    
    B --> C[Backend Router]
    C --> D[FAISS Backend]
    C --> E[Graphiti Backend]
    
    D --> F[Legacy Memory System]
    F --> G[FAISS Vector DB]
    
    E --> H[Graphiti Client]
    H --> I[Neo4j Graph Database]
    
    I --> J[Episodes]
    I --> K[Entities]
    I --> L[Facts/Relationships]
    
    B --> M[Memory Tools]
    B --> N[Memory Extensions]
    
    M --> O[memory_save.py]
    M --> P[memory_load.py]
    M --> Q[memory_delete.py]
    M --> R[memory_forget.py]
    
    N --> S[RecallMemories]
    N --> T[RecallSolutions]
    N --> U[MemorizeMemories]
    N --> V[MemorizeSolutions]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style E fill:#e8f5e8
    style I fill:#f3e5f5
    style J fill:#fce4ec
    style K fill:#fce4ec
    style L fill:#fce4ec
```

### New Components

**Memory Abstraction Layer**
- Unified interface for all memory operations
- Backend switching based on configuration
- Graceful fallback and error handling
- API compatibility with existing tools

**Graphiti Backend**
- Temporal knowledge graph implementation
- Episode-based information storage
- Entity and relationship extraction
- Graph-based semantic search with temporal context

**Neo4j Integration**
- Graph database for complex relationships
- Temporal data with reference times
- Advanced querying capabilities
- Scalable and performant storage

### Enhanced Data Flow

1. **Memory Save**: Text → Episode Creation → Entity Extraction → Graph Storage
2. **Memory Load**: Query → Graph Search → Relationship Traversal → Ranked Results
3. **Temporal Queries**: Time-based filtering and relationship evolution
4. **Context-Aware Recall**: Center node search for personalized results

## Key Improvements

### 1. Temporal Capabilities
- All memories have reference times
- Track information evolution over time
- Query historical states and changes
- Understand temporal relationships

### 2. Relationship Modeling
- Entities and their connections
- Fact-based relationship storage
- Graph traversal for context discovery
- Semantic relationship understanding

### 3. Enhanced Search
- Graph-based semantic search
- Center node personalization
- Relationship-aware ranking
- Multi-hop reasoning capabilities

### 4. Scalability
- Neo4j's proven scalability
- Efficient graph algorithms
- Optimized for relationship queries
- Better performance with large datasets

## Implementation Strategy

### Abstraction Layer Benefits
- **Zero Breaking Changes**: Existing APIs remain unchanged
- **Clean Implementation**: Fresh start without legacy concerns
- **Backend Flexibility**: Easy switching between implementations
- **Future Extensibility**: Ready for additional backend types

### Data Format Design
```
Memory Document (Unified Format)
{
  "id": "episode-uuid-123",
  "content": "User prefers Python for data analysis",
  "metadata": {
    "area": "main",
    "timestamp": "2024-01-01T10:00:00Z",
    "source_description": "agent-zero-main",
    "category": "preference"
  },
  "score": 0.85  // For search results
}

Graphiti Episode (Backend Format)
{
  "name": "Memory: Main",
  "episode_body": "User prefers Python for data analysis",
  "source": "text",
  "reference_time": "2024-01-01T10:00:00Z",
  "source_description": "agent-zero-main"
}
```

## Configuration Management

### Environment Variables
```bash
# Backend Selection
MEMORY_BACKEND=graphiti|faiss

# Graphiti Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
GRAPHITI_GROUP_ID=agent-zero-default

# Feature Flags
GRAPHITI_ENABLED=true|false
MIGRATION_MODE=true|false
```

### Agent Configuration
```python
@dataclass
class AgentConfig:
    # ... existing config ...
    
    # Graphiti settings
    graphiti_enabled: bool = False
    graphiti_neo4j_uri: str = "bolt://localhost:7687"
    graphiti_neo4j_user: str = "neo4j"
    graphiti_neo4j_password: str = "password"
    graphiti_group_id: str = "default"
```

## Performance Considerations

### FAISS vs Graphiti
| Aspect | FAISS | Graphiti |
|--------|-------|----------|
| Search Speed | Very Fast | Fast |
| Relationship Queries | Limited | Excellent |
| Temporal Queries | None | Native |
| Scalability | Good | Excellent |
| Memory Usage | Low | Moderate |
| Setup Complexity | Simple | Moderate |

### Optimization Strategies
- **Indexing**: Proper Neo4j indices for performance
- **Caching**: Cache frequently accessed nodes/relationships
- **Batch Operations**: Bulk inserts for better performance
- **Query Optimization**: Efficient Cypher queries

## Security Considerations

### Data Protection
- Neo4j authentication and authorization
- Encrypted connections (TLS)
- Secure credential management
- Network isolation for database

### Access Control
- User-based data isolation via group_id
- Role-based access to memory operations
- Audit logging for sensitive operations

## Monitoring and Observability

### Key Metrics
- Memory operation latency
- Neo4j database performance
- Error rates and types
- Memory usage patterns
- Query complexity and performance

### Logging
- Structured logging for all memory operations
- Performance metrics collection
- Error tracking and alerting
- Migration progress monitoring

## Future Enhancements

### Planned Features
- **Multi-modal Memory**: Support for images, audio, video
- **Advanced Reasoning**: Graph neural networks for inference
- **Collaborative Memory**: Shared knowledge across agent instances
- **Memory Compression**: Intelligent summarization of old memories
- **Real-time Updates**: Live memory updates during conversations

### Integration Opportunities
- **Knowledge Graphs**: Integration with external knowledge bases
- **Semantic Web**: RDF/OWL compatibility
- **Machine Learning**: Graph-based ML for memory insights
- **Analytics**: Memory usage analytics and optimization
