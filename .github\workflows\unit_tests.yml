name: Unit Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: depot-ubuntu-22.04
    environment:
      name: development
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Load cached Poetry installation
        uses: actions/cache@v4
        with:
          path: ~/.local
          key: poetry-${{ runner.os }}-${{ hashFiles('**/poetry.lock') }}
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true
      - name: Install dependencies
        run: poetry install --no-interaction --no-root
      - name: Run non-integration tests
        env:
          PYTHONPATH: ${{ github.workspace }}
        run: |
          poetry run pytest -m "not integration"
