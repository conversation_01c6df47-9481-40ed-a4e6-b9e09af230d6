#!/usr/bin/env python3
"""
Test script for permanent SSE connection to Graphiti MCP server.

This script demonstrates:
1. Starting a permanent SSE server
2. Connecting with an SSE client that stays alive
3. Using async add_episode with background processing
4. Polling for completion status

Usage:
    1. First, start the permanent server:
       python run_permanent_sse_server.py
    
    2. Then run this test script:
       python test_sse_permanent_connection.py
"""

import asyncio
import json
import logging
import time
from utils.mcp_client_sse import GraphitiSSEClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_permanent_connection():
    """Test the permanent SSE connection with async operations."""
    logger.info("🧪 Testing Permanent SSE Connection to Graphiti MCP Server")
    logger.info("=" * 60)
    
    # Connect to the SSE server
    async with GraphitiSSEClient() as client:
        logger.info("✅ Connected to permanent SSE server")
        
        # Test 1: List available tools
        logger.info("\n1️⃣ Testing tool listing...")
        tools = await client.list_tools()
        logger.info(f"Available tools: {[tool['name'] for tool in tools]}")
        
        # Test 2: Get initial status
        logger.info("\n2️⃣ Getting initial status...")
        status = await client.get_status()
        logger.info(f"Initial status: {status}")
        
        # Test 3: Add memory asynchronously (let background workers handle it)
        logger.info("\n3️⃣ Adding memory asynchronously...")
        
        test_episodes = [
            {
                "name": "SSE Test Episode 1",
                "episode_body": json.dumps({
                    "type": "test",
                    "content": "Testing SSE permanent connection",
                    "timestamp": time.time(),
                    "test_id": "sse_test_1"
                }),
                "group_id": "sse-test-group",
                "source": "json",
                "source_description": "SSE connection test"
            },
            {
                "name": "SSE Test Episode 2", 
                "episode_body": "This is a plain text episode for testing the SSE connection and background processing.",
                "group_id": "sse-test-group",
                "source": "text",
                "source_description": "SSE text test"
            },
            {
                "name": "SSE Test Episode 3",
                "episode_body": json.dumps({
                    "conversation": [
                        {"role": "user", "content": "How does the SSE connection work?"},
                        {"role": "assistant", "content": "SSE allows the server to push updates to the client over a persistent HTTP connection."}
                    ],
                    "metadata": {"test_type": "conversation", "connection": "sse"}
                }),
                "group_id": "sse-test-group", 
                "source": "json",
                "source_description": "SSE conversation test"
            }
        ]
        
        # Add all episodes asynchronously
        results = []
        for episode in test_episodes:
            logger.info(f"📝 Adding episode: {episode['name']}")
            result = await client.add_memory_async(**episode)
            results.append(result)
            logger.info(f"✅ Episode added: {result}")
            
            # Small delay between episodes
            await asyncio.sleep(0.5)
        
        # Test 4: Poll for completion
        logger.info("\n4️⃣ Polling for background processing completion...")
        completion_success = await client.poll_for_completion(
            group_id="sse-test-group",
            timeout=30.0,
            poll_interval=2.0
        )
        
        if completion_success:
            logger.info("✅ Background processing completed successfully")
        else:
            logger.warning("⏰ Background processing may still be ongoing")
        
        # Test 5: Search for the added memories
        logger.info("\n5️⃣ Searching for added memories...")
        search_results = await client.search_memory_nodes(
            query="SSE test connection",
            group_id="sse-test-group",
            limit=10
        )
        logger.info(f"Search results: {search_results}")
        
        # Test 6: Keep connection alive and demonstrate persistence
        logger.info("\n6️⃣ Demonstrating persistent connection...")
        logger.info("Connection will remain alive for 30 seconds...")
        
        for i in range(6):  # 6 iterations of 5 seconds each
            await asyncio.sleep(5)
            
            # Ping the server to show connection is alive
            status = await client.get_status()
            logger.info(f"⏰ {(i+1)*5}s - Connection alive, status: {status.get('status', 'unknown')}")
            
            # Add a small episode to show ongoing functionality
            if i == 2:  # At 15 seconds, add another episode
                logger.info("📝 Adding another episode to demonstrate ongoing functionality...")
                result = await client.add_memory_async(
                    name=f"Persistent Connection Test {time.time()}",
                    episode_body=f"Added at {time.time()} to demonstrate persistent SSE connection",
                    group_id="sse-test-group",
                    source="text",
                    source_description="Persistence test"
                )
                logger.info(f"✅ Persistent episode added: {result}")
        
        logger.info("\n✅ All tests completed successfully!")
        logger.info("🔌 Connection will be closed when exiting context manager")


async def test_multiple_concurrent_operations():
    """Test multiple concurrent operations on the same connection."""
    logger.info("\n🔄 Testing concurrent operations...")
    
    async with GraphitiSSEClient() as client:
        # Create multiple concurrent tasks
        tasks = []
        
        for i in range(3):
            task = client.add_memory_async(
                name=f"Concurrent Episode {i+1}",
                episode_body=f"This is concurrent episode {i+1} testing parallel operations",
                group_id="concurrent-test",
                source="text",
                source_description=f"Concurrent test {i+1}"
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Concurrent task {i+1} failed: {result}")
            else:
                logger.info(f"✅ Concurrent task {i+1} completed: {result}")


async def main():
    """Main test function."""
    try:
        # Test basic permanent connection
        await test_permanent_connection()
        
        # Test concurrent operations
        await test_multiple_concurrent_operations()
        
        logger.info("\n🎉 All SSE tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n👋 Test interrupted by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        exit(1)
